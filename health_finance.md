Next.js Health & Finance Tracker - Production Website

Build a comprehensive, production-ready web application using Next.js 14+ with the following specifications:

🎯 Project Overview
Create a modern health and fitness tracking platform with integrated expense management, featuring beautiful UI/UX, user authentication, and third-party health app integrations.

🛠️ Tech Stack Requirements

Core Framework & Libraries
- Next.js 14+ with App Router
- TypeScript (strict mode)
- Tailwind CSS for styling
- Framer Motion for animations
- React Hook Form + Zod for form validation
- Zustand or Redux Toolkit for state management

Database & Backend
- Prisma ORM with PostgreSQL (or Supabase)
- NextAuth.js for authentication
- Uploadthing or Cloudinary for file uploads
- Vercel deployment ready

UI Components & Design
- shadcn/ui component library
- Radix UI primitives
- Lucide React icons
- Recharts for data visualization
- React Hot Toast for notifications

Health Data Integration
- Google Fit API integration
- Samsung Health API integration (if available)
- Manual data entry forms

📱 Core Features

1. User Authentication & Onboarding
```typescript
// Implement comprehensive auth system
- Email/password registration and login
- Social login (Google, Apple)
- Email verification
- Password reset functionality
- User profile setup wizard
- Terms of service and privacy policy acceptance
```

2. Health & Fitness Dashboard
```typescript
// Main dashboard features
- Overview cards with key metrics
- Weekly/monthly progress charts
- Goal tracking with progress bars
- Recent activity timeline
- Quick action buttons for data entry
- Responsive design for mobile/desktop
```

3. Health Data Management
```typescript
// Health tracking capabilities
- Manual data entry forms for:
  * Weight tracking
  * Blood pressure
  * Heart rate
  * Steps and activity
  * Sleep tracking
  * Nutrition logging
  * Custom health metrics

- Third-party integrations:
  * Google Fit API connection
  * Samsung Health sync (where available)
  * Data synchronization and conflict resolution
  * Import/export functionality
```

4. Expense Management System
```typescript
// Financial tracking features
- Daily expense entry with categories
- Receipt photo upload and OCR
- Expense categorization (Healthcare, Fitness, Food, etc.)
- Weekly/monthly spending reports
- Budget setting and tracking
- Spending trends and analytics
- Export to CSV/PDF
```

5. Reports & Analytics
```typescript
// Advanced reporting system
- Interactive charts and graphs
- Health progress reports
- Expense analysis dashboards
- Goal achievement tracking
- Exportable reports (PDF)
- Data insights and recommendations
```

🎨 UI/UX Requirements

Design System
```css
/* Implement modern, clean design */
- Dark/light theme toggle
- Consistent color palette
- Smooth animations and transitions
- Mobile-first responsive design
- Accessibility compliance (WCAG 2.1)
- Loading states and skeleton screens
- Error boundaries and fallback UI
```

Key Design Elements
- Hero section with compelling value proposition
- Gradient backgrounds and modern card designs
- Interactive data visualizations
- Smooth page transitions
- Intuitive navigation with breadcrumbs
- Progress indicators for multi-step processes
- Empty states with helpful illustrations

🏗️ Project Structure
```
src/
├── app/                    Next.js app router
│   ├── (auth)/            Auth pages
│   ├── (dashboard)/       Protected dashboard
│   ├── api/               API routes
│   └── globals.css
├── components/            Reusable components
│   ├── ui/               shadcn/ui components
│   ├── forms/            Form components
│   ├── charts/           Chart components
│   └── layouts/          Layout components
├── lib/                  Utilities and configurations
│   ├── auth.ts           NextAuth config
│   ├── db.ts             Database connection
│   ├── utils.ts          Helper functions
│   └── validations.ts    Zod schemas
├── hooks/                Custom React hooks
├── stores/               State management
├── types/                TypeScript type definitions
└── styles/               Additional styles
```

🔐 Security & Performance

Security Features
```typescript
// Implement production-level security
- Input validation and sanitization
- Rate limiting on API routes
- CSRF protection
- Secure cookie handling
- Data encryption for sensitive information
- API key management with environment variables
```

Performance Optimizations
```typescript
// Ensure optimal performance
- Image optimization with next/image
- Code splitting and lazy loading
- Caching strategies (React Query/SWR)
- Bundle size optimization
- Core Web Vitals optimization
- Progressive Web App (PWA) features
```

📊 Database Schema (Prisma)
```prisma
// Core models to implement
model User {
  id          String   @id @default(cuid())
  email       String   @unique
  name        String?
  image       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  healthData  HealthData[]
  expenses    Expense[]
  goals       Goal[]
}

model HealthData {
  id          String   @id @default(cuid())
  userId      String
  type        String   // weight, steps, heart_rate, etc.
  value       Float
  unit        String
  recordedAt  DateTime
  source      String   // manual, google_fit, samsung_health
  
  user        User     @relation(fields: [userId], references: [id])
}

model Expense {
  id          String   @id @default(cuid())
  userId      String
  amount      Float
  category    String
  description String?
  date        DateTime
  receiptUrl  String?
  
  user        User     @relation(fields: [userId], references: [id])
}
```

🔌 API Integration Examples
```typescript
// Google Fit API integration
async function syncGoogleFitData(accessToken: string) {
  // Implement Google Fit data synchronization
}

// Samsung Health integration
async function syncSamsungHealthData(accessToken: string) {
  // Implement Samsung Health data sync
}

// Expense OCR processing
async function processReceiptImage(imageFile: File) {
  // Extract expense data from receipt images
}
```

📱 Mobile Responsiveness
- Progressive Web App capabilities
- Touch-friendly interface
- Swipe gestures for data entry
- Offline functionality with service workers
- Push notifications for reminders

🚀 Deployment & DevOps
```bash
Production deployment setup
- Vercel deployment configuration
- Environment variable management
- Database migrations
- CI/CD pipeline setup
- Error monitoring (Sentry)
- Analytics integration (Vercel Analytics)
- Performance monitoring
```

📝 Additional Features to Implement
- Goal setting and achievement tracking
- Social features (optional sharing)
- Data export functionality
- Notification system for reminders
- Multi-language support
- Accessibility features
- Terms of service and privacy policy pages
- Help documentation and FAQ section

🎯 Success Criteria
1. Fast loading times (<3s initial load)
2. Mobile-responsive design
3. Secure authentication system
4. Real-time data synchronization
5. Intuitive user experience
6. Production-ready code quality
7. SEO optimized
8. GDPR compliant data handling

Note: Ensure all API keys and sensitive data are properly secured using environment variables. Implement proper error handling, loading states, and user feedback throughout the application. Follow Next.js best practices for SEO and performance optimization.